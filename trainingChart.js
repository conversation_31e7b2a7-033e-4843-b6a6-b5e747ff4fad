// 训练图表可视化模块
class TrainingChart {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // 数据存储
        this.fitnessHistory = [];
        this.maxDataPoints = 50; // 最多显示50代数据
        
        // 图表配置
        this.config = {
            padding: {
                top: 20,
                right: 20,
                bottom: 30,
                left: 40
            },
            colors: {
                background: '#1a1a1a',
                grid: '#333',
                axis: '#666',
                text: '#fff',
                bestFitness: '#4CAF50',
                avgFitness: '#2196F3',
                worstFitness: '#FF5722'
            },
            lineWidth: 2,
            pointRadius: 3,
            fontSize: 10
        };
        
        // 计算绘图区域
        this.plotArea = {
            x: this.config.padding.left,
            y: this.config.padding.top,
            width: this.width - this.config.padding.left - this.config.padding.right,
            height: this.height - this.config.padding.top - this.config.padding.bottom
        };
        
        // 数据范围
        this.dataRange = {
            minX: 0,
            maxX: 10,
            minY: 0,
            maxY: 100
        };
        
        this.init();
    }

    init() {
        // 初始化画布
        this.clear();
        this.drawBackground();
        this.drawAxes();
        this.drawLabels();
    }

    // 添加训练数据
    addData(generation, bestFitness, avgFitness, worstFitness) {
        this.fitnessHistory.push({
            generation: generation,
            best: bestFitness,
            average: avgFitness,
            worst: worstFitness,
            timestamp: Date.now()
        });
        
        // 限制数据点数量
        if (this.fitnessHistory.length > this.maxDataPoints) {
            this.fitnessHistory.shift();
        }
        
        // 更新数据范围
        this.updateDataRange();
        
        // 重绘图表
        this.render();
    }

    // 更新数据范围
    updateDataRange() {
        if (this.fitnessHistory.length === 0) return;
        
        const generations = this.fitnessHistory.map(d => d.generation);
        const allFitness = this.fitnessHistory.flatMap(d => [d.best, d.average, d.worst]);
        
        this.dataRange.minX = Math.min(...generations);
        this.dataRange.maxX = Math.max(...generations);
        this.dataRange.minY = Math.max(0, Math.min(...allFitness) * 0.9);
        this.dataRange.maxY = Math.max(...allFitness) * 1.1;
        
        // 确保有合理的范围
        if (this.dataRange.maxX === this.dataRange.minX) {
            this.dataRange.maxX = this.dataRange.minX + 10;
        }
        if (this.dataRange.maxY === this.dataRange.minY) {
            this.dataRange.maxY = this.dataRange.minY + 100;
        }
    }

    // 坐标转换
    dataToScreen(dataX, dataY) {
        const x = this.plotArea.x + (dataX - this.dataRange.minX) / 
                  (this.dataRange.maxX - this.dataRange.minX) * this.plotArea.width;
        const y = this.plotArea.y + this.plotArea.height - 
                  (dataY - this.dataRange.minY) / (this.dataRange.maxY - this.dataRange.minY) * this.plotArea.height;
        return { x, y };
    }

    // 清空画布
    clear() {
        this.ctx.fillStyle = this.config.colors.background;
        this.ctx.fillRect(0, 0, this.width, this.height);
    }

    // 绘制背景
    drawBackground() {
        this.ctx.fillStyle = this.config.colors.background;
        this.ctx.fillRect(this.plotArea.x, this.plotArea.y, this.plotArea.width, this.plotArea.height);
    }

    // 绘制坐标轴
    drawAxes() {
        this.ctx.strokeStyle = this.config.colors.axis;
        this.ctx.lineWidth = 1;
        
        // X轴
        this.ctx.beginPath();
        this.ctx.moveTo(this.plotArea.x, this.plotArea.y + this.plotArea.height);
        this.ctx.lineTo(this.plotArea.x + this.plotArea.width, this.plotArea.y + this.plotArea.height);
        this.ctx.stroke();
        
        // Y轴
        this.ctx.beginPath();
        this.ctx.moveTo(this.plotArea.x, this.plotArea.y);
        this.ctx.lineTo(this.plotArea.x, this.plotArea.y + this.plotArea.height);
        this.ctx.stroke();
    }

    // 绘制网格
    drawGrid() {
        this.ctx.strokeStyle = this.config.colors.grid;
        this.ctx.lineWidth = 0.5;
        
        // 垂直网格线
        const xSteps = 5;
        for (let i = 0; i <= xSteps; i++) {
            const x = this.plotArea.x + (i / xSteps) * this.plotArea.width;
            this.ctx.beginPath();
            this.ctx.moveTo(x, this.plotArea.y);
            this.ctx.lineTo(x, this.plotArea.y + this.plotArea.height);
            this.ctx.stroke();
        }
        
        // 水平网格线
        const ySteps = 4;
        for (let i = 0; i <= ySteps; i++) {
            const y = this.plotArea.y + (i / ySteps) * this.plotArea.height;
            this.ctx.beginPath();
            this.ctx.moveTo(this.plotArea.x, y);
            this.ctx.lineTo(this.plotArea.x + this.plotArea.width, y);
            this.ctx.stroke();
        }
    }

    // 绘制标签
    drawLabels() {
        this.ctx.fillStyle = this.config.colors.text;
        this.ctx.font = `${this.config.fontSize}px Arial`;
        this.ctx.textAlign = 'center';
        
        // X轴标签
        this.ctx.fillText('代数', this.plotArea.x + this.plotArea.width / 2, this.height - 5);
        
        // Y轴标签
        this.ctx.save();
        this.ctx.translate(10, this.plotArea.y + this.plotArea.height / 2);
        this.ctx.rotate(-Math.PI / 2);
        this.ctx.fillText('适应度', 0, 0);
        this.ctx.restore();
        
        // 刻度标签
        this.drawTickLabels();
    }

    // 绘制刻度标签
    drawTickLabels() {
        this.ctx.fillStyle = this.config.colors.text;
        this.ctx.font = `${this.config.fontSize}px Arial`;
        
        // X轴刻度
        this.ctx.textAlign = 'center';
        const xSteps = 5;
        for (let i = 0; i <= xSteps; i++) {
            const dataX = this.dataRange.minX + (i / xSteps) * (this.dataRange.maxX - this.dataRange.minX);
            const screenX = this.plotArea.x + (i / xSteps) * this.plotArea.width;
            this.ctx.fillText(Math.round(dataX).toString(), screenX, this.plotArea.y + this.plotArea.height + 15);
        }
        
        // Y轴刻度
        this.ctx.textAlign = 'right';
        const ySteps = 4;
        for (let i = 0; i <= ySteps; i++) {
            const dataY = this.dataRange.minY + (i / ySteps) * (this.dataRange.maxY - this.dataRange.minY);
            const screenY = this.plotArea.y + this.plotArea.height - (i / ySteps) * this.plotArea.height;
            this.ctx.fillText(Math.round(dataY).toString(), this.plotArea.x - 5, screenY + 3);
        }
    }

    // 绘制数据线
    drawDataLine(data, color, label) {
        if (data.length < 2) return;
        
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = this.config.lineWidth;
        this.ctx.beginPath();
        
        for (let i = 0; i < data.length; i++) {
            const point = this.dataToScreen(data[i].x, data[i].y);
            if (i === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        }
        
        this.ctx.stroke();
        
        // 绘制数据点
        this.ctx.fillStyle = color;
        for (const point of data) {
            const screenPoint = this.dataToScreen(point.x, point.y);
            this.ctx.beginPath();
            this.ctx.arc(screenPoint.x, screenPoint.y, this.config.pointRadius, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    // 绘制图例
    drawLegend() {
        const legendItems = [
            { color: this.config.colors.bestFitness, label: '最佳' },
            { color: this.config.colors.avgFitness, label: '平均' },
            { color: this.config.colors.worstFitness, label: '最差' }
        ];
        
        const legendX = this.plotArea.x + this.plotArea.width - 80;
        const legendY = this.plotArea.y + 10;
        
        this.ctx.font = `${this.config.fontSize}px Arial`;
        this.ctx.textAlign = 'left';
        
        for (let i = 0; i < legendItems.length; i++) {
            const item = legendItems[i];
            const y = legendY + i * 15;
            
            // 绘制颜色块
            this.ctx.fillStyle = item.color;
            this.ctx.fillRect(legendX, y - 5, 10, 10);
            
            // 绘制标签
            this.ctx.fillStyle = this.config.colors.text;
            this.ctx.fillText(item.label, legendX + 15, y + 3);
        }
    }

    // 渲染图表
    render() {
        this.clear();
        this.drawBackground();
        this.drawGrid();
        this.drawAxes();
        this.drawLabels();
        
        if (this.fitnessHistory.length > 0) {
            // 准备数据
            const bestData = this.fitnessHistory.map(d => ({ x: d.generation, y: d.best }));
            const avgData = this.fitnessHistory.map(d => ({ x: d.generation, y: d.average }));
            const worstData = this.fitnessHistory.map(d => ({ x: d.generation, y: d.worst }));
            
            // 绘制数据线
            this.drawDataLine(worstData, this.config.colors.worstFitness, '最差');
            this.drawDataLine(avgData, this.config.colors.avgFitness, '平均');
            this.drawDataLine(bestData, this.config.colors.bestFitness, '最佳');
            
            // 绘制图例
            this.drawLegend();
        }
    }

    // 导出图表数据
    exportData() {
        const data = {
            timestamp: new Date().toISOString(),
            fitnessHistory: this.fitnessHistory,
            config: this.config
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `training_data_${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        Utils.log('训练数据已导出', 'info');
    }

    // 重置图表
    reset() {
        this.fitnessHistory = [];
        this.dataRange = {
            minX: 0,
            maxX: 10,
            minY: 0,
            maxY: 100
        };
        this.init();
    }

    // 获取统计信息
    getStats() {
        if (this.fitnessHistory.length === 0) {
            return {
                convergence: 0,
                diversity: 100,
                improvement: 0,
                stability: 0
            };
        }
        
        const recent = this.fitnessHistory.slice(-10);
        const bestValues = recent.map(d => d.best);
        const avgValues = recent.map(d => d.average);
        
        // 计算收敛度（最佳和平均的接近程度）
        const convergence = recent.length > 0 ? 
            (1 - (recent[recent.length - 1].best - recent[recent.length - 1].average) / recent[recent.length - 1].best) * 100 : 0;
        
        // 计算多样性（最佳和最差的差异程度）
        const diversity = recent.length > 0 ? 
            (recent[recent.length - 1].best - recent[recent.length - 1].worst) / recent[recent.length - 1].best * 100 : 100;
        
        // 计算改进趋势
        const improvement = bestValues.length > 1 ? 
            (bestValues[bestValues.length - 1] - bestValues[0]) / bestValues[0] * 100 : 0;
        
        // 计算稳定性（方差的倒数）
        const variance = this.calculateVariance(bestValues);
        const stability = variance > 0 ? Math.min(100, 1000 / variance) : 100;
        
        return {
            convergence: Math.max(0, Math.min(100, convergence)),
            diversity: Math.max(0, Math.min(100, diversity)),
            improvement: improvement,
            stability: Math.max(0, Math.min(100, stability))
        };
    }

    // 计算方差
    calculateVariance(values) {
        if (values.length < 2) return 0;
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }
}

// 导出训练图表类
window.TrainingChart = TrainingChart;
