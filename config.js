// 游戏配置管理系统
class GameConfig {
    constructor() {
        // 游戏基础配置
        this.game = {
            canvas: {
                width: 800,
                height: 600,
                backgroundColor: '#0a0a0a'
            },
            targetFPS: 60,
            maxDeltaTime: 50, // 最大帧时间，防止卡顿时物理计算异常
            debugMode: false
        };

        // 赛车配置
        this.car = {
            physics: {
                maxSpeed: 5,
                acceleration: 0.2,
                friction: 0.95,
                turnSpeed: 0.03
            },
            dimensions: {
                width: 20,
                height: 10
            },
            sensors: {
                count: 5,
                length: 150,
                angles: [-Math.PI/2, -Math.PI/4, 0, Math.PI/4, Math.PI/2]
            },
            colors: {
                alive: null, // 随机生成
                dead: '#666',
                sensor: 'rgba(255, 255, 0, 0.3)',
                sensorHit: 'red'
            },
            stagnation: {
                maxStagnantTime: 180, // 3秒不动淘汰 (60fps * 3s)
                minMovementThreshold: 8, // 最小移动距离阈值
                maxLifeTime: 900 // 个体最大存活时间15秒 (60fps * 15s)
            }
        };

        // 道路配置
        this.road = {
            generation: {
                segmentLength: 20,
                noiseScale: 0.01,
                curvatureStrength: 100,
                widthVariation: 0.005
            },
            dimensions: {
                defaultWidth: 120,
                minWidth: 80,
                maxWidth: 160
            },
            obstacles: {
                frequency: 0.02,
                minCount: 1,
                maxCount: 3,
                types: ['stone', 'barrel', 'cone'],
                sizes: {
                    min: 15,
                    max: 25
                }
            },
            rendering: {
                surfaceColor: '#333',
                borderColor: '#555',
                markingColor: '#fff',
                markingDash: [20, 20]
            }
        };

        // 神经网络配置
        this.neuralNetwork = {
            architecture: {
                inputSize: 5,
                hiddenLayers: [8, 6],
                outputSize: 2
            },
            activation: {
                hidden: 'leakyRelu',
                output: 'tanh'
            },
            initialization: {
                method: 'xavier',
                biasRange: [-0.1, 0.1]
            },
            constraints: {
                weightLimit: 5,
                biasLimit: 5
            },
            visualization: {
                nodeRadius: 8,
                connectionOpacity: 0.8,
                colors: {
                    input: '#2196F3',
                    hidden: '#4CAF50',
                    output: '#FF9800',
                    positive: 'rgba(76, 175, 80, {alpha})',
                    negative: 'rgba(244, 67, 54, {alpha})'
                }
            }
        };

        // 遗传算法配置
        this.geneticAlgorithm = {
            population: {
                defaultSize: 16,
                minSize: 8,
                maxSize: 32
            },
            selection: {
                method: 'tournament', // 'roulette', 'tournament', 'rank'
                tournamentSize: 3,
                eliteCount: 2
            },
            crossover: {
                method: 'uniform', // 'single', 'two', 'uniform'
                rate: 0.8
            },
            mutation: {
                defaultRate: 0.1,
                minRate: 0.01,
                maxRate: 0.5,
                strength: 0.5,
                adaptive: true
            },
            fitness: {
                distanceWeight: 1.0,
                timeWeight: 0.1,
                speedWeight: 0.5,
                collisionPenalty: 100,
                sensorBonus: 10
            },
            termination: {
                maxGenerations: 1000,
                maxTimePerGeneration: 900, // 15秒 (60fps * 15s)
                fitnessThreshold: 10000,
                noProgressTimeout: 600, // 10秒无进展超时 (60fps * 10s)
                minProgressDistance: 30 // 最小进展距离
            }
        };

        // UI配置
        this.ui = {
            updateInterval: 100, // UI更新间隔(ms)
            colors: {
                primary: '#4CAF50',
                secondary: '#2196F3',
                warning: '#FF9800',
                error: '#F44336',
                background: 'rgba(0, 0, 0, 0.8)',
                text: '#fff'
            },
            animations: {
                buttonHover: 'translateY(-2px)',
                transitionDuration: '0.3s'
            },
            logging: {
                maxEntries: 100,
                types: ['info', 'warning', 'error'],
                colors: {
                    info: 'rgba(76, 175, 80, 0.2)',
                    warning: 'rgba(255, 193, 7, 0.2)',
                    error: 'rgba(244, 67, 54, 0.2)'
                }
            }
        };

        // 性能配置
        this.performance = {
            monitoring: {
                enabled: true,
                fpsUpdateInterval: 1000
            },
            optimization: {
                maxRenderDistance: 1000,
                cullingEnabled: true,
                networkVisualizationFPS: 10 // 降低神经网络可视化更新频率
            }
        };

        // 控制配置
        this.controls = {
            keyboard: {
                forward: ['ArrowUp', 'KeyW'],
                backward: ['ArrowDown', 'KeyS'],
                left: ['ArrowLeft', 'KeyA'],
                right: ['ArrowRight', 'KeyD'],
                shortcuts: {
                    start: 'Space',
                    reset: 'KeyR',
                    manual: 'KeyM',
                    debug: 'KeyF1'
                }
            },
            gameSpeed: {
                min: 0.5,
                max: 5.0,
                step: 0.1,
                default: 1.0
            }
        };

        // 存储配置
        this.storage = {
            keys: {
                bestGenome: 'aiRacer_bestGenome',
                userSettings: 'aiRacer_userSettings',
                trainingHistory: 'aiRacer_trainingHistory'
            },
            autoSave: {
                enabled: true,
                interval: 300000, // 5分钟
                maxHistoryEntries: 50
            }
        };
    }

    // 获取配置值
    get(path) {
        return this.getNestedValue(this, path);
    }

    // 设置配置值
    set(path, value) {
        this.setNestedValue(this, path, value);
    }

    // 获取嵌套对象值
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    // 设置嵌套对象值
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key]) current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }

    // 验证配置
    validate() {
        const errors = [];

        // 验证基础数值范围
        if (this.car.physics.maxSpeed <= 0) {
            errors.push('车辆最大速度必须大于0');
        }

        if (this.car.sensors.count < 3 || this.car.sensors.count > 10) {
            errors.push('传感器数量应在3-10之间');
        }

        if (this.geneticAlgorithm.population.defaultSize < 4) {
            errors.push('种群大小不能少于4');
        }

        if (this.geneticAlgorithm.mutation.defaultRate < 0 || this.geneticAlgorithm.mutation.defaultRate > 1) {
            errors.push('变异率应在0-1之间');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // 重置为默认配置
    reset() {
        const defaultConfig = new GameConfig();
        Object.assign(this, defaultConfig);
        Utils.log('配置已重置为默认值', 'info');
    }

    // 导出配置
    export() {
        return JSON.stringify(this, null, 2);
    }

    // 导入配置
    import(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            Object.assign(this, importedConfig);
            
            const validation = this.validate();
            if (!validation.isValid) {
                Utils.log('导入的配置有错误: ' + validation.errors.join(', '), 'error');
                return false;
            }
            
            Utils.log('配置导入成功', 'info');
            return true;
        } catch (error) {
            Utils.log('配置导入失败: ' + error.message, 'error');
            return false;
        }
    }

    // 保存用户设置到本地存储
    saveUserSettings() {
        try {
            const userSettings = {
                populationSize: this.geneticAlgorithm.population.defaultSize,
                mutationRate: this.geneticAlgorithm.mutation.defaultRate,
                gameSpeed: this.controls.gameSpeed.default,
                debugMode: this.game.debugMode
            };
            
            localStorage.setItem(this.storage.keys.userSettings, JSON.stringify(userSettings));
            Utils.log('用户设置已保存', 'info');
        } catch (error) {
            Utils.log('保存用户设置失败: ' + error.message, 'error');
        }
    }

    // 加载用户设置
    loadUserSettings() {
        try {
            const saved = localStorage.getItem(this.storage.keys.userSettings);
            if (saved) {
                const userSettings = JSON.parse(saved);
                
                if (userSettings.populationSize) {
                    this.geneticAlgorithm.population.defaultSize = userSettings.populationSize;
                }
                if (userSettings.mutationRate) {
                    this.geneticAlgorithm.mutation.defaultRate = userSettings.mutationRate;
                }
                if (userSettings.gameSpeed) {
                    this.controls.gameSpeed.default = userSettings.gameSpeed;
                }
                if (userSettings.debugMode !== undefined) {
                    this.game.debugMode = userSettings.debugMode;
                }
                
                Utils.log('用户设置已加载', 'info');
                return true;
            }
        } catch (error) {
            Utils.log('加载用户设置失败: ' + error.message, 'error');
        }
        return false;
    }

    // 获取性能配置建议
    getPerformanceRecommendations() {
        const recommendations = [];
        
        if (this.geneticAlgorithm.population.defaultSize > 20) {
            recommendations.push('建议降低种群大小以提高性能');
        }
        
        if (this.car.sensors.count > 7) {
            recommendations.push('建议减少传感器数量以提高性能');
        }
        
        if (this.controls.gameSpeed.default > 3) {
            recommendations.push('高游戏速度可能影响训练稳定性');
        }
        
        return recommendations;
    }
}

// 创建全局配置实例
window.gameConfig = new GameConfig();

// 导出配置类
window.GameConfig = GameConfig;
