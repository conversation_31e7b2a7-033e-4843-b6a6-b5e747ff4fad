// 游戏主控制器
class Game {
    constructor() {
        // 加载配置
        this.config = window.gameConfig;

        // 画布和上下文
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.networkCanvas = document.getElementById('networkCanvas');
        this.networkCtx = this.networkCanvas.getContext('2d');

        // 训练图表
        this.trainingChart = new TrainingChart('fitnessChart');

        // 游戏状态
        this.isRunning = false;
        this.isPaused = false;
        this.isManualMode = false;
        this.gameSpeed = this.config.get('controls.gameSpeed.default');
        
        // 游戏对象
        this.road = new Road(this.canvas.width, this.canvas.height);
        this.cars = [];
        this.geneticAlgorithm = new GeneticAlgorithm(
            this.config.get('geneticAlgorithm.population.defaultSize'),
            this.config.get('geneticAlgorithm.mutation.defaultRate'),
            this.config.get('geneticAlgorithm.selection.eliteCount')
        );
        
        // 相机
        this.camera = { x: 0, y: 0 };
        this.followCar = null;
        
        // 性能监控
        this.performanceMonitor = Utils.createPerformanceMonitor();
        this.lastTime = 0;
        this.deltaTime = 0;
        
        // 训练统计
        this.trainingStartTime = 0;
        this.totalTrainingTime = 0;
        this.bestIndividualHistory = [];
        this.successfulGenerations = 0;
        
        // 手动控制
        this.keys = {};
        this.manualCar = null;

        // 性能优化
        this.lastNetworkUpdate = 0;
        this.networkUpdateInterval = 1000 / this.config.get('performance.optimization.networkVisualizationFPS');

        // 进展检测
        this.lastProgressCheck = null;

        // 初始化
        this.init();
    }

    init() {
        try {
            // 验证配置
            const validation = this.config.validate();
            if (!validation.isValid) {
                throw new Error('配置验证失败: ' + validation.errors.join(', '));
            }

            // 加载用户设置
            this.config.loadUserSettings();

            // 创建初始种群
            this.createInitialPopulation();

            // 设置事件监听
            this.setupEventListeners();

            // 设置UI更新
            this.setupUIUpdates();

            // 开始游戏循环
            this.gameLoop();

            Utils.log('游戏初始化完成', 'info');
        } catch (error) {
            Utils.log('游戏初始化失败: ' + error.message, 'error');
            this.handleInitializationError(error);
        }
    }

    handleInitializationError(error) {
        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f44336;
            color: white;
            padding: 20px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 400px;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>初始化错误</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="
                background: white;
                color: #f44336;
                border: none;
                padding: 10px 20px;
                border-radius: 3px;
                cursor: pointer;
                margin-top: 10px;
            ">重新加载</button>
        `;
        document.body.appendChild(errorDiv);
    }

    createInitialPopulation() {
        const startX = this.road.centerX;
        const startY = 100;
        
        this.cars = this.geneticAlgorithm.createInitialPopulation({ x: startX, y: startY });
        this.followCar = this.cars[0];
        
        Utils.log(`创建${this.cars.length}辆AI赛车`, 'info');
    }

    setupEventListeners() {
        // 控制按钮
        document.getElementById('startBtn').addEventListener('click', () => this.startTraining());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseTraining());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
        document.getElementById('manualBtn').addEventListener('click', () => this.toggleManualMode());

        // 新增功能按钮
        document.getElementById('exportDataBtn').addEventListener('click', () => this.exportTrainingData());
        document.getElementById('saveGenomeBtn').addEventListener('click', () => this.saveGenome());
        document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
        
        // 参数滑块
        document.getElementById('populationSize').addEventListener('input', (e) => {
            this.geneticAlgorithm.populationSize = parseInt(e.target.value);
            document.getElementById('populationValue').textContent = e.target.value;
            this.config.set('geneticAlgorithm.population.defaultSize', parseInt(e.target.value));
            this.config.saveUserSettings();
        });

        document.getElementById('mutationRate').addEventListener('input', (e) => {
            this.geneticAlgorithm.mutationRate = parseFloat(e.target.value);
            document.getElementById('mutationValue').textContent = e.target.value;
            this.config.set('geneticAlgorithm.mutation.defaultRate', parseFloat(e.target.value));
            this.config.saveUserSettings();
        });
        
        document.getElementById('gameSpeed').addEventListener('input', (e) => {
            this.gameSpeed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = e.target.value + 'x';
            this.config.set('controls.gameSpeed.default', this.gameSpeed);
            this.config.saveUserSettings();
        });
        
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            // 快捷键
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    this.isRunning ? this.pauseTraining() : this.startTraining();
                    break;
                case 'KeyR':
                    this.resetGame();
                    break;
                case 'KeyM':
                    this.toggleManualMode();
                    break;
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }

    setupUIUpdates() {
        // 定期更新UI
        setInterval(() => {
            this.updateUI();
        }, 100);
    }

    startTraining() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.isPaused = false;
            this.trainingStartTime = Date.now();
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            
            Utils.log('开始AI训练', 'info');
        }
    }

    pauseTraining() {
        if (this.isRunning) {
            this.isPaused = !this.isPaused;
            
            if (this.isPaused) {
                document.getElementById('pauseBtn').textContent = '继续训练';
                Utils.log('训练已暂停', 'warning');
            } else {
                document.getElementById('pauseBtn').textContent = '暂停训练';
                Utils.log('训练已继续', 'info');
            }
        }
    }

    resetGame() {
        this.isRunning = false;
        this.isPaused = false;
        this.isManualMode = false;
        
        // 重置UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('pauseBtn').textContent = '暂停训练';
        document.getElementById('manualBtn').textContent = '手动驾驶';
        
        // 重置游戏对象
        this.road.reset();
        this.geneticAlgorithm.reset();
        this.createInitialPopulation();
        
        // 重置相机
        this.camera = { x: 0, y: 0 };
        
        Utils.log('游戏已重置', 'info');
    }

    toggleManualMode() {
        this.isManualMode = !this.isManualMode;
        
        if (this.isManualMode) {
            this.isRunning = false;
            this.isPaused = false;
            
            // 创建手动控制车辆
            this.manualCar = new Car(this.road.centerX, 100);
            this.manualCar.brain = null; // 禁用AI
            this.followCar = this.manualCar;
            
            document.getElementById('manualBtn').textContent = '退出手动';
            document.getElementById('startBtn').disabled = true;
            
            Utils.log('进入手动驾驶模式', 'info');
        } else {
            this.manualCar = null;
            this.followCar = this.cars[0];
            
            document.getElementById('manualBtn').textContent = '手动驾驶';
            document.getElementById('startBtn').disabled = false;
            
            Utils.log('退出手动驾驶模式', 'info');
        }
    }

    update(deltaTime) {
        if (this.isPaused) return;
        
        const scaledDeltaTime = deltaTime * this.gameSpeed;
        
        if (this.isManualMode && this.manualCar) {
            this.updateManualControl();
            this.manualCar.update(this.road, scaledDeltaTime);
            this.updateCamera();
        } else if (this.isRunning) {
            this.updateAICars(scaledDeltaTime);
            this.updateCamera();
            this.checkGenerationComplete();
        }
    }

    updateManualControl() {
        if (!this.manualCar) return;
        
        this.manualCar.controls.forward = this.keys['ArrowUp'] || this.keys['KeyW'];
        this.manualCar.controls.backward = this.keys['ArrowDown'] || this.keys['KeyS'];
        this.manualCar.controls.left = this.keys['ArrowLeft'] || this.keys['KeyA'];
        this.manualCar.controls.right = this.keys['ArrowRight'] || this.keys['KeyD'];
    }

    updateAICars(deltaTime) {
        for (const car of this.cars) {
            car.update(this.road, deltaTime);
        }
        
        // 更新跟随的车辆（最佳车辆）
        const aliveCars = this.cars.filter(car => car.alive);
        if (aliveCars.length > 0) {
            this.followCar = aliveCars.reduce((best, current) => 
                current.fitness > best.fitness ? current : best
            );
        }
    }

    updateCamera() {
        if (this.followCar) {
            // 平滑跟随
            const targetX = this.followCar.x - this.canvas.width / 2;
            const targetY = this.followCar.y - this.canvas.height / 2;
            
            this.camera.x = Utils.lerp(this.camera.x, targetX, 0.1);
            this.camera.y = Utils.lerp(this.camera.y, targetY, 0.1);
        }
    }

    checkGenerationComplete() {
        const aliveCars = this.cars.filter(car => car.alive);
        const maxGenerationTime = this.config.get('geneticAlgorithm.termination.maxTimePerGeneration');

        // 检查完成条件
        const allDead = aliveCars.length === 0;
        const timeLimit = this.cars.length > 0 && this.cars[0].timeAlive > maxGenerationTime;
        const noProgress = this.checkNoProgressTimeout();

        if (allDead || timeLimit || noProgress) {
            let reason = '所有车辆死亡';
            if (timeLimit) {
                reason = `达到15秒时间限制`;
            } else if (noProgress) {
                reason = '长时间无进展';
            }
            Utils.log(`${reason}，进化到下一代`, 'warning');
            this.evolveGeneration();
        }
    }

    // 检查是否长时间无进展
    checkNoProgressTimeout() {
        if (this.cars.length === 0) return false;

        const bestDistance = Math.max(...this.cars.map(car => car.distance));
        const currentTime = this.cars[0].timeAlive;

        // 检查是否在设定时间内没有显著进展
        if (!this.lastProgressCheck) {
            this.lastProgressCheck = { time: currentTime, distance: bestDistance };
            return false;
        }

        const timeSinceLastCheck = currentTime - this.lastProgressCheck.time;
        const distanceImprovement = bestDistance - this.lastProgressCheck.distance;
        const noProgressTimeout = this.config.get('geneticAlgorithm.termination.noProgressTimeout');
        const minProgressDistance = this.config.get('geneticAlgorithm.termination.minProgressDistance');

        if (timeSinceLastCheck > noProgressTimeout) {
            if (distanceImprovement < minProgressDistance) {
                this.lastProgressCheck = null;
                return true;
            } else {
                this.lastProgressCheck = { time: currentTime, distance: bestDistance };
            }
        }

        return false;
    }

    evolveGeneration() {
        // 记录当前代的统计信息
        const stats = this.geneticAlgorithm.getStats();

        // 更新训练图表
        this.trainingChart.addData(
            stats.generation,
            stats.bestFitness,
            stats.avgFitness,
            this.cars.reduce((min, car) => Math.min(min, car.fitness), Infinity)
        );

        // 检查是否为成功的一代
        if (stats.bestFitness > this.config.get('geneticAlgorithm.fitness.distanceWeight') * 1000) {
            this.successfulGenerations++;
        }

        // 记录最佳个体
        const bestCar = this.cars.find(car => car.fitness === stats.bestFitness);
        if (bestCar) {
            this.bestIndividualHistory.push({
                generation: stats.generation,
                fitness: stats.bestFitness,
                distance: bestCar.distance,
                timeAlive: bestCar.timeAlive
            });
        }

        this.cars = this.geneticAlgorithm.evolveGeneration(this.cars);
        this.followCar = this.cars[0];

        Utils.log(`进化完成，开始第${this.geneticAlgorithm.generation}代训练`, 'info');
    }

    render() {
        // 清空画布
        this.ctx.fillStyle = '#0a0a0a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制道路
        this.road.render(this.ctx, this.camera);
        
        // 绘制车辆（性能优化）
        if (this.isManualMode && this.manualCar) {
            this.manualCar.render(this.ctx, this.camera);
        } else {
            for (const car of this.cars) {
                if (this.shouldRenderCar(car)) {
                    car.render(this.ctx, this.camera);
                }
            }
        }
        
        // 绘制UI覆盖层
        this.renderUI();
        
        // 绘制神经网络（降低更新频率）
        if (this.followCar && this.followCar.brain && this.shouldUpdateNetworkVisualization()) {
            this.followCar.brain.visualize(this.networkCanvas);
            this.lastNetworkUpdate = Date.now();
        }
    }

    renderUI() {
        // FPS显示
        const fps = this.performanceMonitor.getFPS();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, 10, 120, 30);
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.font = '16px Arial';
        this.ctx.fillText(`FPS: ${fps}`, 20, 30);
        
        // 调试信息
        if (this.followCar) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(10, 50, 200, 120);
            this.ctx.fillStyle = '#fff';
            this.ctx.font = '12px Arial';
            this.ctx.fillText(`速度: ${this.followCar.speed.toFixed(1)}`, 20, 70);
            this.ctx.fillText(`距离: ${Utils.formatDistance(this.followCar.distance)}`, 20, 85);
            this.ctx.fillText(`适应度: ${this.followCar.fitness.toFixed(1)}`, 20, 100);
            this.ctx.fillText(`时间: ${Utils.formatTime(this.followCar.timeAlive / 60)}`, 20, 115);

            // 停滞检测信息
            const stagnantPercent = (this.followCar.stagnantTime / this.followCar.maxStagnantTime * 100).toFixed(0);
            this.ctx.fillStyle = this.followCar.stagnantTime > this.followCar.maxStagnantTime * 0.7 ? '#ff6600' : '#fff';
            this.ctx.fillText(`停滞: ${stagnantPercent}%`, 20, 130);

            // 生存时间进度
            const lifePercent = (this.followCar.timeAlive / this.followCar.maxLifeTime * 100).toFixed(0);
            this.ctx.fillStyle = this.followCar.timeAlive > this.followCar.maxLifeTime * 0.8 ? '#ff3333' : '#fff';
            this.ctx.fillText(`生存: ${lifePercent}%`, 20, 145);
        }
    }

    updateUI() {
        // 更新基础统计信息
        document.getElementById('generation').textContent = this.geneticAlgorithm.generation;

        const aliveCars = this.cars.filter(car => car.alive);
        document.getElementById('aliveCars').textContent = aliveCars.length;

        if (this.cars.length > 0) {
            const bestDistance = Math.max(...this.cars.map(car => car.distance));
            document.getElementById('bestDistance').textContent = Utils.formatDistance(bestDistance);

            const avgFitness = this.cars.reduce((sum, car) => sum + car.fitness, 0) / this.cars.length;
            document.getElementById('avgFitness').textContent = avgFitness.toFixed(1);
        }

        // 更新训练时间
        if (this.isRunning && !this.isPaused) {
            const currentTime = (Date.now() - this.trainingStartTime) / 1000;
            document.getElementById('trainingTime').textContent = Utils.formatTime(currentTime);
        }

        // 更新进度条
        if (this.cars.length > 0) {
            const progress = (this.cars.filter(car => !car.alive).length / this.cars.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新详细统计
        this.updateDetailedStats();
    }

    updateDetailedStats() {
        // 最佳个体信息
        if (this.followCar) {
            document.getElementById('bestIndividual').textContent =
                `距离: ${Utils.formatDistance(this.followCar.distance)}, 适应度: ${this.followCar.fitness.toFixed(1)}`;
        }

        // 图表统计
        const chartStats = this.trainingChart.getStats();
        document.getElementById('convergence').textContent = chartStats.convergence.toFixed(1) + '%';
        document.getElementById('diversity').textContent = chartStats.diversity.toFixed(1) + '%';

        // 成功率
        const successRate = this.geneticAlgorithm.generation > 0 ?
            (this.successfulGenerations / this.geneticAlgorithm.generation * 100) : 0;
        document.getElementById('successRate').textContent = successRate.toFixed(1) + '%';
    }

    // 导出训练数据
    exportTrainingData() {
        this.trainingChart.exportData();
    }

    // 保存最佳基因组
    saveGenome() {
        this.geneticAlgorithm.saveBestGenome();
    }

    // 清空日志
    clearLog() {
        const logArea = document.getElementById('logArea');
        logArea.innerHTML = '';
        Utils.log('日志已清空', 'info');
    }

    // 检查是否应该更新神经网络可视化
    shouldUpdateNetworkVisualization() {
        return Date.now() - this.lastNetworkUpdate > this.networkUpdateInterval;
    }

    // 性能优化：限制渲染距离
    shouldRenderCar(car) {
        if (!this.followCar) return true;

        const distance = Utils.distance(car.x, car.y, this.followCar.x, this.followCar.y);
        return distance < this.config.get('performance.optimization.maxRenderDistance');
    }

    gameLoop(currentTime = 0) {
        // 计算deltaTime
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 更新性能监控
        this.performanceMonitor.update();
        
        // 更新游戏
        this.update(this.deltaTime / 16.67); // 标准化到60FPS
        
        // 渲染
        this.render();
        
        // 继续循环
        requestAnimationFrame((time) => this.gameLoop(time));
    }
}

// 游戏启动
window.addEventListener('load', () => {
    window.game = new Game();
    Utils.log('AI赛车强化训练器启动完成！', 'info');
});
