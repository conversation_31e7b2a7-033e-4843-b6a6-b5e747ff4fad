// 赛车类
class Car {
    constructor(x, y, brain = null) {
        // 位置和运动
        this.x = x;
        this.y = y;
        this.angle = 0;
        this.speed = 0;
        this.maxSpeed = 5;
        this.acceleration = 0.2;
        this.friction = 0.95;
        this.turnSpeed = 0.03;
        
        // 物理属性
        this.width = 20;
        this.height = 10;
        this.corners = [];
        
        // AI相关
        this.brain = brain || new NeuralNetwork(5, [8, 6], 2);
        this.sensors = [];
        this.sensorCount = 5;
        this.sensorLength = 150;
        this.sensorAngles = [-Math.PI/2, -Math.PI/4, 0, Math.PI/4, Math.PI/2];
        
        // 状态
        this.alive = true;
        this.fitness = 0;
        this.distance = 0;
        this.timeAlive = 0;
        this.collisionCount = 0;

        // 停滞检测
        this.lastPosition = { x: x, y: y };
        this.stagnantTime = 0;
        this.maxStagnantTime = window.gameConfig ?
            window.gameConfig.get('car.stagnation.maxStagnantTime') : 180;
        this.minMovementThreshold = window.gameConfig ?
            window.gameConfig.get('car.stagnation.minMovementThreshold') : 8;
        this.maxLifeTime = window.gameConfig ?
            window.gameConfig.get('car.stagnation.maxLifeTime') : 900;

        // 转圈检测
        this.positionHistory = [];
        this.circlingDetectionRadius = 50;
        this.maxCirclingTime = 120; // 2秒转圈就淘汰
        this.circlingTime = 0;
        
        // 控制
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false
        };
        
        // 颜色
        this.color = this.generateRandomColor();
        
        // 初始化传感器
        this.initSensors();
    }

    generateRandomColor() {
        const hue = Math.random() * 360;
        const color = Utils.hslToRgb(hue, 70, 50);
        return Utils.rgbToHex(color.r, color.g, color.b);
    }

    initSensors() {
        this.sensors = [];
        for (let i = 0; i < this.sensorCount; i++) {
            this.sensors.push({
                angle: this.sensorAngles[i],
                distance: this.sensorLength,
                x: this.x,
                y: this.y,
                endX: this.x,
                endY: this.y,
                hit: false
            });
        }
    }

    update(road, deltaTime = 1) {
        if (!this.alive) return;

        this.timeAlive += deltaTime;
        
        // 更新传感器
        this.updateSensors(road);
        
        // AI控制
        if (this.brain) {
            this.aiControl();
        }
        
        // 物理更新
        this.updatePhysics(deltaTime);
        
        // 更新车身角点
        this.updateCorners();
        
        // 碰撞检测
        this.checkCollisions(road);
        
        // 检测停滞状态和生存时间
        this.checkStagnation(deltaTime);
        this.checkLifeTimeLimit();
        this.checkCircling(deltaTime);

        // 更新适应度
        this.updateFitness();
    }

    updateSensors(road) {
        for (let i = 0; i < this.sensors.length; i++) {
            const sensor = this.sensors[i];
            const sensorAngle = this.angle + sensor.angle;
            
            sensor.x = this.x;
            sensor.y = this.y;
            sensor.endX = this.x + Math.cos(sensorAngle) * this.sensorLength;
            sensor.endY = this.y + Math.sin(sensorAngle) * this.sensorLength;
            sensor.hit = false;
            sensor.distance = this.sensorLength;
            
            // 检测与道路边界的碰撞
            const roadBounds = road.getRoadBounds(this.y - 100, this.y + this.sensorLength + 100);
            
            for (const bound of roadBounds) {
                for (let j = 0; j < bound.length - 1; j++) {
                    const intersection = Utils.lineIntersection(
                        sensor.x, sensor.y, sensor.endX, sensor.endY,
                        bound[j].x, bound[j].y, bound[j + 1].x, bound[j + 1].y
                    );
                    
                    if (intersection) {
                        const distance = Utils.distance(sensor.x, sensor.y, intersection.x, intersection.y);
                        if (distance < sensor.distance) {
                            sensor.distance = distance;
                            sensor.endX = intersection.x;
                            sensor.endY = intersection.y;
                            sensor.hit = true;
                        }
                    }
                }
            }
        }
    }

    aiControl() {
        // 准备神经网络输入
        const inputs = this.sensors.map(sensor => 
            1 - (sensor.distance / this.sensorLength) // 归一化距离，越近值越大
        );
        
        // 获取神经网络输出
        const outputs = this.brain.feedForward(inputs);
        
        // 解释输出
        const steering = outputs[0]; // [-1, 1] 转向
        const throttle = outputs[1]; // [-1, 1] 油门/刹车
        
        // 应用控制
        this.controls.left = steering < -0.1;
        this.controls.right = steering > 0.1;
        this.controls.forward = throttle > 0.1;
        this.controls.backward = throttle < -0.1;
    }

    updatePhysics(deltaTime) {
        // 转向
        if (this.controls.left) {
            this.angle -= this.turnSpeed * Math.abs(this.speed) * deltaTime;
        }
        if (this.controls.right) {
            this.angle += this.turnSpeed * Math.abs(this.speed) * deltaTime;
        }
        
        // 加速
        if (this.controls.forward) {
            this.speed += this.acceleration * deltaTime;
        }
        if (this.controls.backward) {
            this.speed -= this.acceleration * deltaTime;
        }
        
        // 限制速度
        this.speed = Utils.clamp(this.speed, -this.maxSpeed * 0.5, this.maxSpeed);
        
        // 摩擦力
        this.speed *= this.friction;
        
        // 更新位置
        this.x += Math.cos(this.angle) * this.speed * deltaTime;
        this.y += Math.sin(this.angle) * this.speed * deltaTime;
        
        // 更新行驶距离
        this.distance += Math.abs(this.speed) * deltaTime;
    }

    updateCorners() {
        const cos = Math.cos(this.angle);
        const sin = Math.sin(this.angle);
        const halfWidth = this.width / 2;
        const halfHeight = this.height / 2;
        
        this.corners = [
            {
                x: this.x + cos * halfHeight - sin * halfWidth,
                y: this.y + sin * halfHeight + cos * halfWidth
            },
            {
                x: this.x + cos * halfHeight + sin * halfWidth,
                y: this.y + sin * halfHeight - cos * halfWidth
            },
            {
                x: this.x - cos * halfHeight + sin * halfWidth,
                y: this.y - sin * halfHeight - cos * halfWidth
            },
            {
                x: this.x - cos * halfHeight - sin * halfWidth,
                y: this.y - sin * halfHeight + cos * halfWidth
            }
        ];
    }

    checkCollisions(road) {
        if (!this.alive) return;

        // 获取当前位置的道路信息
        const roadCenter = road.getRoadCenter(this.y);
        const roadWidth = road.getRoadWidth(this.y);
        const halfWidth = roadWidth / 2;

        // 检查车辆的每个角点是否超出道路边界
        for (const corner of this.corners) {
            const distanceFromCenter = corner.x - roadCenter;

            // 如果任何角点超出道路边界，立即碰撞
            if (Math.abs(distanceFromCenter) > halfWidth) {
                this.crash('撞墙');
                return;
            }
        }

        // 额外检查：车辆中心点是否严重偏离
        const centerDistanceFromRoad = Math.abs(this.x - roadCenter);
        if (centerDistanceFromRoad > halfWidth + 10) {
            this.crash('偏离道路');
            return;
        }

        // 检查与障碍物的碰撞
        this.checkObstacleCollisions(road);
    }

    // 检查与障碍物的碰撞
    checkObstacleCollisions(road) {
        const nearbyObstacles = road.getObstaclesInRange(this.y - 30, this.y + 30);

        for (const obstacle of nearbyObstacles) {
            const distance = Utils.distance(this.x, this.y, obstacle.x, obstacle.y);
            const collisionDistance = this.width / 2 + obstacle.size / 2;

            if (distance < collisionDistance) {
                this.crash('撞击障碍物');
                return;
            }
        }
    }

    crash(reason = '碰撞') {
        if (this.alive) {
            this.alive = false;
            this.collisionCount++;
            this.speed = 0;
            Utils.log(`车辆${reason} - 距离: ${Utils.formatDistance(this.distance)}, 时间: ${Utils.formatTime(this.timeAlive / 60)}`, 'warning');
        }
    }

    // 检测停滞状态
    checkStagnation(deltaTime) {
        const currentDistance = Utils.distance(this.x, this.y, this.lastPosition.x, this.lastPosition.y);

        if (currentDistance < this.minMovementThreshold) {
            this.stagnantTime += deltaTime;

            // 如果停滞时间超过限制，淘汰车辆
            if (this.stagnantTime > this.maxStagnantTime) {
                this.crash('停滞超时');
                return;
            }
        } else {
            // 有移动，重置停滞计时器
            this.stagnantTime = 0;
            this.lastPosition.x = this.x;
            this.lastPosition.y = this.y;
        }
    }

    // 检查个体生存时间限制
    checkLifeTimeLimit() {
        if (this.timeAlive > this.maxLifeTime) {
            this.crash('生存时间超限');
        }
    }

    // 检查转圈行为
    checkCircling(deltaTime) {
        // 记录位置历史
        this.positionHistory.push({ x: this.x, y: this.y, time: this.timeAlive });

        // 只保留最近2秒的位置记录
        this.positionHistory = this.positionHistory.filter(pos =>
            this.timeAlive - pos.time < 120
        );

        // 如果位置记录足够多，检查是否在转圈
        if (this.positionHistory.length > 60) { // 1秒的记录
            const isCircling = this.detectCircularMovement();

            if (isCircling) {
                this.circlingTime += deltaTime;
                if (this.circlingTime > this.maxCirclingTime) {
                    this.crash('原地转圈');
                    return;
                }
            } else {
                this.circlingTime = 0;
            }
        }
    }

    // 检测圆周运动
    detectCircularMovement() {
        if (this.positionHistory.length < 60) return false;

        const recent = this.positionHistory.slice(-60); // 最近1秒
        const center = this.calculateCenter(recent);

        // 检查是否所有点都在一个小范围内
        const distances = recent.map(pos =>
            Utils.distance(pos.x, pos.y, center.x, center.y)
        );

        const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
        const maxDistance = Math.max(...distances);
        const minDistance = Math.min(...distances);

        // 如果平均距离小于检测半径，且距离变化不大，认为在转圈
        return avgDistance < this.circlingDetectionRadius &&
               (maxDistance - minDistance) < 20;
    }

    // 计算位置中心点
    calculateCenter(positions) {
        const sumX = positions.reduce((sum, pos) => sum + pos.x, 0);
        const sumY = positions.reduce((sum, pos) => sum + pos.y, 0);
        return {
            x: sumX / positions.length,
            y: sumY / positions.length
        };
    }

    updateFitness() {
        if (!this.alive) return;
        
        // 基础适应度：行驶距离
        let fitness = this.distance;
        
        // 时间奖励
        fitness += this.timeAlive * 0.1;
        
        // 速度奖励（鼓励快速行驶）
        fitness += Math.abs(this.speed) * 0.5;
        
        // 碰撞惩罚
        fitness -= this.collisionCount * 100;
        
        // 传感器奖励（鼓励接近但不碰撞）
        const avgSensorDistance = this.sensors.reduce((sum, sensor) => sum + sensor.distance, 0) / this.sensors.length;
        const normalizedSensorDistance = avgSensorDistance / this.sensorLength;
        
        // 奖励适中的传感器距离（不要太近也不要太远）
        if (normalizedSensorDistance > 0.2 && normalizedSensorDistance < 0.8) {
            fitness += 10;
        }
        
        this.fitness = Math.max(0, fitness);
    }

    render(ctx, camera = { x: 0, y: 0 }) {
        ctx.save();
        
        // 相机偏移
        const screenX = this.x - camera.x;
        const screenY = this.y - camera.y;
        
        // 绘制传感器
        if (this.alive) {
            ctx.strokeStyle = 'rgba(255, 255, 0, 0.3)';
            ctx.lineWidth = 1;
            
            for (const sensor of this.sensors) {
                ctx.beginPath();
                ctx.moveTo(sensor.x - camera.x, sensor.y - camera.y);
                ctx.lineTo(sensor.endX - camera.x, sensor.endY - camera.y);
                ctx.stroke();
                
                if (sensor.hit) {
                    ctx.fillStyle = 'red';
                    ctx.beginPath();
                    ctx.arc(sensor.endX - camera.x, sensor.endY - camera.y, 3, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
        }
        
        // 绘制车身
        ctx.translate(screenX, screenY);
        ctx.rotate(this.angle);
        
        // 车身颜色
        ctx.fillStyle = this.alive ? this.color : '#666';
        ctx.strokeStyle = this.alive ? '#fff' : '#333';
        ctx.lineWidth = 2;
        
        // 绘制车身矩形
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 绘制车头指示
        ctx.fillStyle = this.alive ? '#fff' : '#999';
        ctx.fillRect(this.width/2 - 3, -2, 6, 4);
        
        ctx.restore();
    }

    // 复制车辆（用于遗传算法）
    copy() {
        const copy = new Car(this.x, this.y, this.brain.copy());
        copy.color = this.color;
        return copy;
    }

    // 重置车辆状态
    reset(x, y) {
        this.x = x;
        this.y = y;
        this.angle = 0;
        this.speed = 0;
        this.alive = true;
        this.fitness = 0;
        this.distance = 0;
        this.timeAlive = 0;
        this.collisionCount = 0;

        // 重置停滞检测
        this.lastPosition = { x: x, y: y };
        this.stagnantTime = 0;

        // 重置转圈检测
        this.positionHistory = [];
        this.circlingTime = 0;

        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false
        };
    }
}

// 导出赛车类
window.Car = Car;
