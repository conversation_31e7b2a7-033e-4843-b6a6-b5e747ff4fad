// 道路生成系统
class Road {
    constructor(width = 800, height = 600) {
        this.width = width;
        this.height = height;
        
        // 道路参数
        this.roadWidth = 120;
        this.minRoadWidth = 80;
        this.maxRoadWidth = 160;
        this.centerX = width / 2;
        
        // 生成参数
        this.segmentLength = 20;
        this.noiseScale = 0.003; // 增加噪声频率，产生更多弯道
        this.curvatureStrength = 150; // 增加弯道强度
        this.widthVariation = 0.008; // 增加宽度变化
        
        // 道路数据
        this.segments = [];
        this.roadBounds = { left: [], right: [] };
        this.generatedLength = 0;
        this.maxGeneratedLength = 5000;
        
        // 噪声生成器
        this.noiseGenerator = Utils.createPerlinNoise();
        this.widthNoiseGenerator = Utils.createPerlinNoise();
        
        // 障碍物
        this.obstacles = [];
        this.obstacleFrequency = 0.03; // 增加障碍物频率
        
        // 初始化道路
        this.generateInitialRoad();
    }

    generateInitialRoad() {
        // 生成初始道路段
        for (let y = 0; y < this.maxGeneratedLength; y += this.segmentLength) {
            this.generateSegment(y);
        }
        
        Utils.log('道路初始化完成', 'info');
    }

    generateSegment(y) {
        // 使用多层噪声生成更复杂的道路中心线
        const primaryNoise = this.noiseGenerator(0, y * this.noiseScale);
        const secondaryNoise = this.noiseGenerator(50, y * this.noiseScale * 2) * 0.5;
        const tertiaryNoise = this.noiseGenerator(100, y * this.noiseScale * 4) * 0.25;

        // 组合多层噪声创建更自然的弯道
        const combinedNoise = primaryNoise + secondaryNoise + tertiaryNoise;
        const centerX = this.centerX + combinedNoise * this.curvatureStrength;

        // 添加正弦波形成周期性弯道
        const sineWave = Math.sin(y * 0.001) * 80;
        let finalCenterX = centerX + sineWave;

        // 限制道路不要偏离屏幕太远
        const margin = 100;
        finalCenterX = Utils.clamp(finalCenterX, margin, this.canvasWidth - margin);
        
        // 使用另一个噪声生成道路宽度变化
        const widthNoise = this.widthNoiseGenerator(100, y * this.widthVariation);
        const roadWidth = this.roadWidth + widthNoise * (this.maxRoadWidth - this.minRoadWidth) * 0.5;
        
        // 创建道路段
        const segment = {
            y: y,
            centerX: finalCenterX,
            width: roadWidth,
            leftX: finalCenterX - roadWidth / 2,
            rightX: finalCenterX + roadWidth / 2,
            obstacles: []
        };
        
        // 随机生成障碍物
        if (Math.random() < this.obstacleFrequency && y > 500) {
            this.generateObstacles(segment);
        }
        
        this.segments.push(segment);
        this.generatedLength = Math.max(this.generatedLength, y);
    }

    generateObstacles(segment) {
        const obstacleCount = Utils.randomInt(1, 3);
        
        for (let i = 0; i < obstacleCount; i++) {
            const obstacle = {
                x: Utils.randomRange(segment.leftX + 20, segment.rightX - 20),
                y: segment.y + Utils.randomRange(-10, 10),
                width: Utils.randomRange(15, 25),
                height: Utils.randomRange(15, 25),
                type: Utils.randomInt(0, 2) // 0: 石块, 1: 油桶, 2: 锥形桶
            };
            
            segment.obstacles.push(obstacle);
            this.obstacles.push(obstacle);
        }
    }

    extendRoad(targetY) {
        // 动态扩展道路
        while (this.generatedLength < targetY + 1000) {
            this.generateSegment(this.generatedLength + this.segmentLength);
        }
    }

    getRoadCenter(y) {
        const segment = this.getSegmentAt(y);
        if (!segment) return this.centerX;
        
        // 线性插值获得精确位置
        const nextSegment = this.getSegmentAt(y + this.segmentLength);
        if (!nextSegment) return segment.centerX;
        
        const t = (y - segment.y) / this.segmentLength;
        return Utils.lerp(segment.centerX, nextSegment.centerX, t);
    }

    getRoadWidth(y) {
        const segment = this.getSegmentAt(y);
        if (!segment) return this.roadWidth;
        
        const nextSegment = this.getSegmentAt(y + this.segmentLength);
        if (!nextSegment) return segment.width;
        
        const t = (y - segment.y) / this.segmentLength;
        return Utils.lerp(segment.width, nextSegment.width, t);
    }

    getRoadBounds(startY, endY) {
        const bounds = [];
        const leftBound = [];
        const rightBound = [];
        
        const step = 10;
        for (let y = startY; y <= endY; y += step) {
            const centerX = this.getRoadCenter(y);
            const width = this.getRoadWidth(y);
            
            leftBound.push({ x: centerX - width / 2, y: y });
            rightBound.push({ x: centerX + width / 2, y: y });
        }
        
        // 创建闭合的道路边界多边形
        const roadBoundary = [
            ...leftBound,
            ...rightBound.reverse()
        ];
        
        // 返回左右边界作为碰撞检测用
        bounds.push(leftBound);
        bounds.push(rightBound.reverse());
        
        return bounds;
    }

    getSegmentAt(y) {
        const index = Math.floor(y / this.segmentLength);
        return this.segments[index] || null;
    }

    getObstaclesInRange(startY, endY) {
        return this.obstacles.filter(obstacle => 
            obstacle.y >= startY && obstacle.y <= endY
        );
    }

    render(ctx, camera = { x: 0, y: 0 }) {
        const startY = camera.y - this.height / 2;
        const endY = camera.y + this.height / 2;
        
        // 确保道路足够长
        this.extendRoad(endY);
        
        // 绘制道路背景
        this.renderRoadSurface(ctx, camera, startY, endY);
        
        // 绘制道路标线
        this.renderRoadMarkings(ctx, camera, startY, endY);
        
        // 绘制障碍物
        this.renderObstacles(ctx, camera, startY, endY);
        
        // 绘制道路边界（调试用）
        if (window.DEBUG_MODE) {
            this.renderRoadBounds(ctx, camera, startY, endY);
        }
    }

    renderRoadSurface(ctx, camera, startY, endY) {
        ctx.fillStyle = '#333';
        ctx.strokeStyle = '#555';
        ctx.lineWidth = 2;
        
        const step = 5;
        const points = [];
        
        // 生成道路轮廓点
        for (let y = startY; y <= endY; y += step) {
            const centerX = this.getRoadCenter(y);
            const width = this.getRoadWidth(y);
            
            points.push({
                leftX: centerX - width / 2,
                rightX: centerX + width / 2,
                y: y
            });
        }
        
        // 绘制道路表面
        ctx.beginPath();
        
        // 左边界
        for (let i = 0; i < points.length; i++) {
            const screenX = points[i].leftX - camera.x;
            const screenY = points[i].y - camera.y;
            
            if (i === 0) {
                ctx.moveTo(screenX, screenY);
            } else {
                ctx.lineTo(screenX, screenY);
            }
        }
        
        // 右边界（反向）
        for (let i = points.length - 1; i >= 0; i--) {
            const screenX = points[i].rightX - camera.x;
            const screenY = points[i].y - camera.y;
            ctx.lineTo(screenX, screenY);
        }
        
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }

    renderRoadMarkings(ctx, camera, startY, endY) {
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.setLineDash([20, 20]);
        
        // 绘制中心线
        ctx.beginPath();
        for (let y = startY; y <= endY; y += 10) {
            const centerX = this.getRoadCenter(y);
            const screenX = centerX - camera.x;
            const screenY = y - camera.y;
            
            if (y === startY) {
                ctx.moveTo(screenX, screenY);
            } else {
                ctx.lineTo(screenX, screenY);
            }
        }
        ctx.stroke();
        
        ctx.setLineDash([]); // 重置虚线
    }

    renderObstacles(ctx, camera, startY, endY) {
        const obstacles = this.getObstaclesInRange(startY, endY);
        
        for (const obstacle of obstacles) {
            const screenX = obstacle.x - camera.x;
            const screenY = obstacle.y - camera.y;
            
            ctx.save();
            ctx.translate(screenX, screenY);
            
            switch (obstacle.type) {
                case 0: // 石块
                    ctx.fillStyle = '#666';
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.fillRect(-obstacle.width/2, -obstacle.height/2, obstacle.width, obstacle.height);
                    ctx.strokeRect(-obstacle.width/2, -obstacle.height/2, obstacle.width, obstacle.height);
                    break;
                    
                case 1: // 油桶
                    ctx.fillStyle = '#ff6600';
                    ctx.strokeStyle = '#cc4400';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(0, 0, obstacle.width/2, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                    
                    // 油桶条纹
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(-obstacle.width/3, -obstacle.height/4);
                    ctx.lineTo(obstacle.width/3, -obstacle.height/4);
                    ctx.moveTo(-obstacle.width/3, obstacle.height/4);
                    ctx.lineTo(obstacle.width/3, obstacle.height/4);
                    ctx.stroke();
                    break;
                    
                case 2: // 锥形桶
                    ctx.fillStyle = '#ff9900';
                    ctx.strokeStyle = '#ff6600';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(0, -obstacle.height/2);
                    ctx.lineTo(-obstacle.width/2, obstacle.height/2);
                    ctx.lineTo(obstacle.width/2, obstacle.height/2);
                    ctx.closePath();
                    ctx.fill();
                    ctx.stroke();
                    
                    // 白色条纹
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(-obstacle.width/4, 0);
                    ctx.lineTo(obstacle.width/4, 0);
                    ctx.stroke();
                    break;
            }
            
            ctx.restore();
        }
    }

    renderRoadBounds(ctx, camera, startY, endY) {
        const bounds = this.getRoadBounds(startY, endY);
        
        ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        ctx.lineWidth = 1;
        
        for (const bound of bounds) {
            ctx.beginPath();
            for (let i = 0; i < bound.length; i++) {
                const screenX = bound[i].x - camera.x;
                const screenY = bound[i].y - camera.y;
                
                if (i === 0) {
                    ctx.moveTo(screenX, screenY);
                } else {
                    ctx.lineTo(screenX, screenY);
                }
            }
            ctx.stroke();
        }
    }

    // 获取道路统计信息
    getStats() {
        return {
            totalSegments: this.segments.length,
            generatedLength: this.generatedLength,
            totalObstacles: this.obstacles.length,
            avgRoadWidth: this.segments.reduce((sum, seg) => sum + seg.width, 0) / this.segments.length
        };
    }

    // 重置道路
    reset() {
        this.segments = [];
        this.obstacles = [];
        this.generatedLength = 0;
        this.generateInitialRoad();
        Utils.log('道路已重置', 'info');
    }
}

// 导出道路类
window.Road = Road;
