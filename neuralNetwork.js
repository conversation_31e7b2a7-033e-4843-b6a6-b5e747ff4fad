// 神经网络模块
class NeuralNetwork {
    constructor(inputSize = 5, hiddenSizes = [8, 6], outputSize = 2) {
        this.inputSize = inputSize;
        this.hiddenSizes = hiddenSizes;
        this.outputSize = outputSize;
        this.layers = [];
        
        // 构建网络层
        this.buildNetwork();
        
        // 初始化权重和偏置
        this.initializeWeights();
    }

    buildNetwork() {
        const sizes = [this.inputSize, ...this.hiddenSizes, this.outputSize];
        
        for (let i = 0; i < sizes.length - 1; i++) {
            this.layers.push({
                weights: [],
                biases: new Array(sizes[i + 1]).fill(0),
                activations: new Array(sizes[i + 1]).fill(0),
                inputSize: sizes[i],
                outputSize: sizes[i + 1]
            });
        }
    }

    initializeWeights() {
        this.layers.forEach(layer => {
            // Xavier初始化
            const limit = Math.sqrt(6 / (layer.inputSize + layer.outputSize));
            
            layer.weights = [];
            for (let i = 0; i < layer.inputSize; i++) {
                layer.weights[i] = [];
                for (let j = 0; j < layer.outputSize; j++) {
                    layer.weights[i][j] = Utils.randomRange(-limit, limit);
                }
            }
            
            // 偏置初始化为小随机值
            for (let i = 0; i < layer.outputSize; i++) {
                layer.biases[i] = Utils.randomRange(-0.1, 0.1);
            }
        });
    }

    // 激活函数
    static sigmoid(x) {
        return 1 / (1 + Math.exp(-Math.max(-500, Math.min(500, x))));
    }

    static tanh(x) {
        return Math.tanh(x);
    }

    static relu(x) {
        return Math.max(0, x);
    }

    static leakyRelu(x, alpha = 0.01) {
        return x > 0 ? x : alpha * x;
    }

    // 前向传播
    feedForward(inputs) {
        if (inputs.length !== this.inputSize) {
            throw new Error(`输入大小不匹配: 期望 ${this.inputSize}, 得到 ${inputs.length}`);
        }

        let currentInputs = [...inputs];

        for (let layerIndex = 0; layerIndex < this.layers.length; layerIndex++) {
            const layer = this.layers[layerIndex];
            const outputs = new Array(layer.outputSize);

            for (let j = 0; j < layer.outputSize; j++) {
                let sum = layer.biases[j];
                
                for (let i = 0; i < layer.inputSize; i++) {
                    sum += currentInputs[i] * layer.weights[i][j];
                }

                // 根据层选择激活函数
                if (layerIndex === this.layers.length - 1) {
                    // 输出层使用tanh，输出范围[-1, 1]
                    outputs[j] = NeuralNetwork.tanh(sum);
                } else {
                    // 隐藏层使用ReLU
                    outputs[j] = NeuralNetwork.leakyRelu(sum);
                }
            }

            layer.activations = outputs;
            currentInputs = outputs;
        }

        return currentInputs;
    }

    // 获取网络权重（用于遗传算法）
    getWeights() {
        const weights = [];
        
        this.layers.forEach(layer => {
            // 添加权重
            layer.weights.forEach(row => {
                weights.push(...row);
            });
            // 添加偏置
            weights.push(...layer.biases);
        });
        
        return weights;
    }

    // 设置网络权重（用于遗传算法）
    setWeights(weights) {
        let index = 0;
        
        this.layers.forEach(layer => {
            // 设置权重
            for (let i = 0; i < layer.inputSize; i++) {
                for (let j = 0; j < layer.outputSize; j++) {
                    layer.weights[i][j] = weights[index++];
                }
            }
            // 设置偏置
            for (let i = 0; i < layer.outputSize; i++) {
                layer.biases[i] = weights[index++];
            }
        });
    }

    // 复制网络
    copy() {
        const copy = new NeuralNetwork(this.inputSize, this.hiddenSizes, this.outputSize);
        copy.setWeights(this.getWeights());
        return copy;
    }

    // 变异（用于遗传算法）
    mutate(mutationRate = 0.1, mutationStrength = 0.5) {
        this.layers.forEach(layer => {
            // 变异权重
            for (let i = 0; i < layer.inputSize; i++) {
                for (let j = 0; j < layer.outputSize; j++) {
                    if (Math.random() < mutationRate) {
                        layer.weights[i][j] += Utils.randomRange(-mutationStrength, mutationStrength);
                        layer.weights[i][j] = Utils.clamp(layer.weights[i][j], -5, 5);
                    }
                }
            }
            
            // 变异偏置
            for (let i = 0; i < layer.outputSize; i++) {
                if (Math.random() < mutationRate) {
                    layer.biases[i] += Utils.randomRange(-mutationStrength, mutationStrength);
                    layer.biases[i] = Utils.clamp(layer.biases[i], -5, 5);
                }
            }
        });
    }

    // 交叉（用于遗传算法）
    static crossover(parent1, parent2) {
        const child1 = parent1.copy();
        const child2 = parent2.copy();
        
        const weights1 = parent1.getWeights();
        const weights2 = parent2.getWeights();
        
        // 单点交叉
        const crossoverPoint = Math.floor(Math.random() * weights1.length);
        
        const childWeights1 = [
            ...weights1.slice(0, crossoverPoint),
            ...weights2.slice(crossoverPoint)
        ];
        
        const childWeights2 = [
            ...weights2.slice(0, crossoverPoint),
            ...weights1.slice(crossoverPoint)
        ];
        
        child1.setWeights(childWeights1);
        child2.setWeights(childWeights2);
        
        return [child1, child2];
    }

    // 获取网络信息
    getNetworkInfo() {
        const totalWeights = this.layers.reduce((sum, layer) => 
            sum + layer.inputSize * layer.outputSize + layer.outputSize, 0);
        
        return {
            inputSize: this.inputSize,
            hiddenSizes: this.hiddenSizes,
            outputSize: this.outputSize,
            totalLayers: this.layers.length,
            totalWeights: totalWeights,
            architecture: [this.inputSize, ...this.hiddenSizes, this.outputSize].join('-')
        };
    }

    // 可视化网络结构
    visualize(canvas, bestNetwork = null) {
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        ctx.clearRect(0, 0, width, height);
        
        const layers = [this.inputSize, ...this.hiddenSizes, this.outputSize];
        const layerSpacing = width / (layers.length + 1);
        const maxNodes = Math.max(...layers);
        
        // 绘制连接线
        for (let l = 0; l < layers.length - 1; l++) {
            const currentLayer = layers[l];
            const nextLayer = layers[l + 1];
            
            for (let i = 0; i < currentLayer; i++) {
                for (let j = 0; j < nextLayer; j++) {
                    const x1 = layerSpacing * (l + 1);
                    const y1 = (height / (currentLayer + 1)) * (i + 1);
                    const x2 = layerSpacing * (l + 2);
                    const y2 = (height / (nextLayer + 1)) * (j + 1);
                    
                    // 根据权重设置线条颜色和粗细
                    if (this.layers[l] && this.layers[l].weights[i] && this.layers[l].weights[i][j] !== undefined) {
                        const weight = this.layers[l].weights[i][j];
                        const intensity = Math.abs(weight) / 2; // 假设权重范围[-2, 2]
                        const alpha = Math.min(0.8, intensity);
                        
                        ctx.strokeStyle = weight > 0 ? 
                            `rgba(76, 175, 80, ${alpha})` : 
                            `rgba(244, 67, 54, ${alpha})`;
                        ctx.lineWidth = Math.max(0.5, intensity * 2);
                    } else {
                        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
                        ctx.lineWidth = 0.5;
                    }
                    
                    ctx.beginPath();
                    ctx.moveTo(x1, y1);
                    ctx.lineTo(x2, y2);
                    ctx.stroke();
                }
            }
        }
        
        // 绘制节点
        for (let l = 0; l < layers.length; l++) {
            const nodeCount = layers[l];
            const x = layerSpacing * (l + 1);
            
            for (let i = 0; i < nodeCount; i++) {
                const y = (height / (nodeCount + 1)) * (i + 1);
                
                // 节点颜色
                let nodeColor = '#4CAF50';
                if (l === 0) nodeColor = '#2196F3'; // 输入层
                else if (l === layers.length - 1) nodeColor = '#FF9800'; // 输出层
                
                // 绘制节点
                ctx.fillStyle = nodeColor;
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 绘制节点边框
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        }
        
        // 绘制标签
        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        const labels = ['输入', ...Array(this.hiddenSizes.length).fill('隐藏'), '输出'];
        for (let l = 0; l < layers.length; l++) {
            const x = layerSpacing * (l + 1);
            ctx.fillText(labels[l], x, height - 10);
        }
    }
}

// 导出神经网络类
window.NeuralNetwork = NeuralNetwork;
