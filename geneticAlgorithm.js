// 遗传算法模块
class GeneticAlgorithm {
    constructor(populationSize = 16, mutationRate = 0.1, eliteCount = 2) {
        this.populationSize = populationSize;
        this.mutationRate = mutationRate;
        this.eliteCount = eliteCount;
        this.generation = 0;
        this.bestFitness = 0;
        this.avgFitness = 0;
        this.fitnessHistory = [];
        this.bestGenomes = [];
        
        // 选择方法
        this.selectionMethod = 'tournament'; // 'roulette', 'tournament', 'rank'
        this.tournamentSize = 3;
        
        // 交叉方法
        this.crossoverMethod = 'uniform'; // 'single', 'two', 'uniform'
        this.crossoverRate = 0.8;
        
        // 变异参数
        this.mutationStrength = 0.5;
        this.adaptiveMutation = true;
        
        Utils.log('遗传算法初始化完成', 'info');
    }

    // 创建初始种群
    createInitialPopulation(carTemplate) {
        const population = [];
        
        for (let i = 0; i < this.populationSize; i++) {
            const car = new Car(carTemplate.x, carTemplate.y);
            car.brain = new NeuralNetwork(5, [8, 6], 2);
            population.push(car);
        }
        
        Utils.log(`创建初始种群: ${this.populationSize} 个个体`, 'info');
        return population;
    }

    // 评估种群适应度
    evaluatePopulation(population) {
        // 计算适应度统计
        const fitnesses = population.map(car => car.fitness);
        this.bestFitness = Math.max(...fitnesses);
        this.avgFitness = fitnesses.reduce((sum, f) => sum + f, 0) / fitnesses.length;
        
        // 记录历史
        this.fitnessHistory.push({
            generation: this.generation,
            best: this.bestFitness,
            average: this.avgFitness,
            worst: Math.min(...fitnesses)
        });
        
        // 保存最佳基因组
        const bestCar = population.find(car => car.fitness === this.bestFitness);
        if (bestCar) {
            this.bestGenomes.push({
                generation: this.generation,
                fitness: this.bestFitness,
                genome: bestCar.brain.getWeights()
            });
        }
        
        Utils.log(`第${this.generation}代评估完成 - 最佳: ${this.bestFitness.toFixed(2)}, 平均: ${this.avgFitness.toFixed(2)}`, 'info');
    }

    // 选择操作
    selection(population) {
        switch (this.selectionMethod) {
            case 'roulette':
                return this.rouletteSelection(population);
            case 'tournament':
                return this.tournamentSelection(population);
            case 'rank':
                return this.rankSelection(population);
            default:
                return this.tournamentSelection(population);
        }
    }

    // 轮盘赌选择
    rouletteSelection(population) {
        const fitnesses = population.map(car => Math.max(0, car.fitness));
        const totalFitness = fitnesses.reduce((sum, f) => sum + f, 0);
        
        if (totalFitness === 0) {
            return population[Utils.randomInt(0, population.length - 1)];
        }
        
        const weights = fitnesses.map(f => f / totalFitness);
        return Utils.weightedRandomSelect(population, weights);
    }

    // 锦标赛选择
    tournamentSelection(population) {
        const tournament = [];
        
        for (let i = 0; i < this.tournamentSize; i++) {
            const randomIndex = Utils.randomInt(0, population.length - 1);
            tournament.push(population[randomIndex]);
        }
        
        return tournament.reduce((best, current) => 
            current.fitness > best.fitness ? current : best
        );
    }

    // 排名选择
    rankSelection(population) {
        const sorted = [...population].sort((a, b) => b.fitness - a.fitness);
        const ranks = sorted.map((_, index) => population.length - index);
        const totalRank = ranks.reduce((sum, rank) => sum + rank, 0);
        const weights = ranks.map(rank => rank / totalRank);
        
        return Utils.weightedRandomSelect(sorted, weights);
    }

    // 交叉操作
    crossover(parent1, parent2) {
        if (Math.random() > this.crossoverRate) {
            return [parent1.copy(), parent2.copy()];
        }
        
        switch (this.crossoverMethod) {
            case 'single':
                return this.singlePointCrossover(parent1, parent2);
            case 'two':
                return this.twoPointCrossover(parent1, parent2);
            case 'uniform':
                return this.uniformCrossover(parent1, parent2);
            default:
                return this.uniformCrossover(parent1, parent2);
        }
    }

    // 单点交叉
    singlePointCrossover(parent1, parent2) {
        const weights1 = parent1.brain.getWeights();
        const weights2 = parent2.brain.getWeights();
        
        const crossoverPoint = Utils.randomInt(1, weights1.length - 1);
        
        const child1Weights = [
            ...weights1.slice(0, crossoverPoint),
            ...weights2.slice(crossoverPoint)
        ];
        
        const child2Weights = [
            ...weights2.slice(0, crossoverPoint),
            ...weights1.slice(crossoverPoint)
        ];
        
        const child1 = parent1.copy();
        const child2 = parent2.copy();
        
        child1.brain.setWeights(child1Weights);
        child2.brain.setWeights(child2Weights);
        
        return [child1, child2];
    }

    // 两点交叉
    twoPointCrossover(parent1, parent2) {
        const weights1 = parent1.brain.getWeights();
        const weights2 = parent2.brain.getWeights();
        
        const point1 = Utils.randomInt(1, weights1.length - 2);
        const point2 = Utils.randomInt(point1 + 1, weights1.length - 1);
        
        const child1Weights = [
            ...weights1.slice(0, point1),
            ...weights2.slice(point1, point2),
            ...weights1.slice(point2)
        ];
        
        const child2Weights = [
            ...weights2.slice(0, point1),
            ...weights1.slice(point1, point2),
            ...weights2.slice(point2)
        ];
        
        const child1 = parent1.copy();
        const child2 = parent2.copy();
        
        child1.brain.setWeights(child1Weights);
        child2.brain.setWeights(child2Weights);
        
        return [child1, child2];
    }

    // 均匀交叉
    uniformCrossover(parent1, parent2) {
        const weights1 = parent1.brain.getWeights();
        const weights2 = parent2.brain.getWeights();
        
        const child1Weights = [];
        const child2Weights = [];
        
        for (let i = 0; i < weights1.length; i++) {
            if (Math.random() < 0.5) {
                child1Weights[i] = weights1[i];
                child2Weights[i] = weights2[i];
            } else {
                child1Weights[i] = weights2[i];
                child2Weights[i] = weights1[i];
            }
        }
        
        const child1 = parent1.copy();
        const child2 = parent2.copy();
        
        child1.brain.setWeights(child1Weights);
        child2.brain.setWeights(child2Weights);
        
        return [child1, child2];
    }

    // 变异操作
    mutate(individual) {
        let currentMutationRate = this.mutationRate;
        
        // 自适应变异率
        if (this.adaptiveMutation) {
            // 如果适应度停滞，增加变异率
            const recentGenerations = this.fitnessHistory.slice(-5);
            if (recentGenerations.length >= 5) {
                const fitnessVariance = this.calculateVariance(recentGenerations.map(g => g.best));
                if (fitnessVariance < 10) {
                    currentMutationRate *= 2;
                }
            }
        }
        
        individual.brain.mutate(currentMutationRate, this.mutationStrength);
    }

    // 计算方差
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }

    // 进化到下一代
    evolveGeneration(population) {
        // 评估当前种群
        this.evaluatePopulation(population);
        
        // 按适应度排序
        const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
        
        // 精英保留
        const newPopulation = [];
        for (let i = 0; i < this.eliteCount; i++) {
            newPopulation.push(sortedPopulation[i].copy());
        }
        
        // 生成新个体
        while (newPopulation.length < this.populationSize) {
            const parent1 = this.selection(population);
            const parent2 = this.selection(population);
            
            const [child1, child2] = this.crossover(parent1, parent2);
            
            this.mutate(child1);
            if (newPopulation.length + 1 < this.populationSize) {
                this.mutate(child2);
                newPopulation.push(child1, child2);
            } else {
                newPopulation.push(child1);
            }
        }
        
        // 重置新种群
        for (let i = 0; i < newPopulation.length; i++) {
            newPopulation[i].reset(population[0].x, population[0].y);
        }
        
        this.generation++;
        Utils.log(`进化到第${this.generation}代`, 'info');
        
        return newPopulation;
    }

    // 获取统计信息
    getStats() {
        return {
            generation: this.generation,
            populationSize: this.populationSize,
            bestFitness: this.bestFitness,
            avgFitness: this.avgFitness,
            mutationRate: this.mutationRate,
            fitnessHistory: this.fitnessHistory,
            bestGenomes: this.bestGenomes
        };
    }

    // 保存最佳个体
    saveBestGenome() {
        if (this.bestGenomes.length > 0) {
            const best = this.bestGenomes[this.bestGenomes.length - 1];
            const data = JSON.stringify(best, null, 2);
            
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `best_genome_gen${best.generation}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            Utils.log('最佳基因组已保存', 'info');
        }
    }

    // 加载基因组
    loadGenome(genomeData) {
        try {
            const genome = JSON.parse(genomeData);
            Utils.log(`加载基因组: 第${genome.generation}代, 适应度: ${genome.fitness}`, 'info');
            return genome.genome;
        } catch (error) {
            Utils.log('基因组加载失败: ' + error.message, 'error');
            return null;
        }
    }

    // 重置算法
    reset() {
        this.generation = 0;
        this.bestFitness = 0;
        this.avgFitness = 0;
        this.fitnessHistory = [];
        this.bestGenomes = [];
        Utils.log('遗传算法已重置', 'info');
    }
}

// 导出遗传算法类
window.GeneticAlgorithm = GeneticAlgorithm;
