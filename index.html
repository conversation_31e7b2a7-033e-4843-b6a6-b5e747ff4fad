<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI赛车强化训练器</title>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.15.0/dist/tf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .game-area {
            flex: 1;
            position: relative;
            background: #000;
        }

        #gameCanvas {
            display: block;
            background: #1a1a1a;
            border: 2px solid #333;
        }

        .control-panel {
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            overflow-y: auto;
        }

        .panel-section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel-section h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 16px;
        }

        .control-button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .control-button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
        }

        .control-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            color: #4CAF50;
            font-weight: bold;
        }

        .slider-container {
            margin: 10px 0;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }

        .slider {
            width: 100%;
            height: 5px;
            border-radius: 5px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
        }

        .neural-network-viz, .fitness-chart {
            width: 100%;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            margin-top: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }

        .chart-controls .control-button {
            flex: 1;
        }

        .generation-info {
            text-align: center;
            font-size: 18px;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s;
        }

        .log-area {
            height: 150px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #ccc;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .log-info { background: rgba(76, 175, 80, 0.2); }
        .log-warning { background: rgba(255, 193, 7, 0.2); }
        .log-error { background: rgba(244, 67, 54, 0.2); }
    </style>
</head>
<body>
    <div class="container">
        <div class="game-area">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
        </div>

        <div class="control-panel">
            <!-- 训练控制 -->
            <div class="panel-section">
                <h3>🚗 训练控制</h3>
                <button id="startBtn" class="control-button">开始训练</button>
                <button id="pauseBtn" class="control-button" disabled>暂停训练</button>
                <button id="resetBtn" class="control-button">重置</button>
                <button id="manualBtn" class="control-button">手动驾驶</button>
            </div>

            <!-- 当前状态 -->
            <div class="panel-section">
                <h3>📊 当前状态</h3>
                <div class="generation-info">第 <span id="generation">0</span> 代</div>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">存活车辆:</span>
                    <span id="aliveCars" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最佳距离:</span>
                    <span id="bestDistance" class="stat-value">0m</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均适应度:</span>
                    <span id="avgFitness" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">训练时间:</span>
                    <span id="trainingTime" class="stat-value">00:00</span>
                </div>
            </div>

            <!-- 训练参数 -->
            <div class="panel-section">
                <h3>⚙️ 训练参数</h3>
                <div class="slider-container">
                    <label for="populationSize">种群大小: <span id="populationValue">16</span></label>
                    <input type="range" id="populationSize" class="slider" min="8" max="32" value="16">
                </div>
                <div class="slider-container">
                    <label for="mutationRate">变异率: <span id="mutationValue">0.1</span></label>
                    <input type="range" id="mutationRate" class="slider" min="0.01" max="0.5" step="0.01" value="0.1">
                </div>
                <div class="slider-container">
                    <label for="gameSpeed">游戏速度: <span id="speedValue">1.0x</span></label>
                    <input type="range" id="gameSpeed" class="slider" min="0.5" max="5.0" step="0.1" value="1.0">
                </div>
            </div>

            <!-- 神经网络可视化 -->
            <div class="panel-section">
                <h3>🧠 神经网络</h3>
                <canvas id="networkCanvas" class="neural-network-viz" width="260" height="180"></canvas>
            </div>

            <!-- 训练图表 -->
            <div class="panel-section">
                <h3>📈 训练图表</h3>
                <canvas id="fitnessChart" class="fitness-chart" width="260" height="120"></canvas>
                <div class="chart-controls">
                    <button id="exportDataBtn" class="control-button" style="font-size: 12px; padding: 5px;">导出数据</button>
                    <button id="saveGenomeBtn" class="control-button" style="font-size: 12px; padding: 5px;">保存基因组</button>
                </div>
            </div>

            <!-- 详细统计 -->
            <div class="panel-section">
                <h3>📊 详细统计</h3>
                <div class="stat-item">
                    <span class="stat-label">最佳个体:</span>
                    <span id="bestIndividual" class="stat-value">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">收敛度:</span>
                    <span id="convergence" class="stat-value">0%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">多样性:</span>
                    <span id="diversity" class="stat-value">100%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">成功率:</span>
                    <span id="successRate" class="stat-value">0%</span>
                </div>
            </div>

            <!-- 训练日志 -->
            <div class="panel-section">
                <h3>📝 训练日志</h3>
                <div id="logArea" class="log-area"></div>
                <button id="clearLogBtn" class="control-button" style="font-size: 12px; padding: 5px; margin-top: 5px;">清空日志</button>
            </div>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.15.0/dist/tf.min.js"></script>
    <script src="config.js"></script>
    <script src="utils.js"></script>
    <script src="neuralNetwork.js"></script>
    <script src="car.js"></script>
    <script src="road.js"></script>
    <script src="geneticAlgorithm.js"></script>
    <script src="trainingChart.js"></script>
    <script src="game.js"></script>
</body>
</html>